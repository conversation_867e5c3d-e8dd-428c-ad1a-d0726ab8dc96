#!/usr/bin/env python3
"""
DeepSeek API 测试脚本
用于验证 DeepSeek API 配置是否正确
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_deepseek_api():
    """测试 DeepSeek API 连接"""
    
    # 获取配置
    api_key = os.getenv('AI_API_KEY')
    api_url = os.getenv('AI_API_URL')
    
    print("=== DeepSeek API 配置测试 ===")
    print(f"API Key: {'已配置' if api_key else '未配置'}")
    print(f"API URL: {api_url}")
    print()
    
    if not api_key or not api_url:
        print("❌ 错误: AI_API_KEY 或 AI_API_URL 未配置")
        print("请检查 .env 文件中的配置")
        return False
    
    # 构建测试请求
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'model': 'deepseek-chat',
        'messages': [
            {
                'role': 'system',
                'content': '你是一个AI助手，请简洁地回答问题。'
            },
            {
                'role': 'user',
                'content': '你好，请简单介绍一下你自己。'
            }
        ],
        'max_tokens': 100,
        'temperature': 0.7
    }
    
    print("🔄 正在测试 API 连接...")
    
    try:
        response = requests.post(
            api_url,
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            ai_reply = result['choices'][0]['message']['content']
            
            print("✅ API 测试成功!")
            print(f"🤖 AI 回复: {ai_reply}")
            print()
            
            # 显示使用统计
            if 'usage' in result:
                usage = result['usage']
                print("📈 Token 使用统计:")
                print(f"   - 输入 tokens: {usage.get('prompt_tokens', 'N/A')}")
                print(f"   - 输出 tokens: {usage.get('completion_tokens', 'N/A')}")
                print(f"   - 总计 tokens: {usage.get('total_tokens', 'N/A')}")
            
            return True
            
        else:
            print(f"❌ API 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，请检查网络连接")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_model_list():
    """测试获取可用模型列表"""
    api_key = os.getenv('AI_API_KEY')
    
    if not api_key:
        print("❌ API Key 未配置，跳过模型列表测试")
        return
    
    print("\n=== 获取可用模型列表 ===")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(
            'https://api.deepseek.com/v1/models',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            models = response.json()
            print("✅ 可用模型:")
            for model in models.get('data', []):
                print(f"   - {model.get('id', 'Unknown')}")
        else:
            print(f"❌ 获取模型列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取模型列表异常: {e}")

if __name__ == '__main__':
    print("DeepSeek API 测试工具")
    print("=" * 50)
    
    # 测试基本 API 功能
    success = test_deepseek_api()
    
    # 测试模型列表
    test_model_list()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 DeepSeek API 配置正确，可以正常使用！")
    else:
        print("⚠️  DeepSeek API 配置有问题，请检查配置后重试。")
    
    sys.exit(0 if success else 1)
